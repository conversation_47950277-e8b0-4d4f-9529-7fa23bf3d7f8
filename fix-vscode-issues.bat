@echo off
echo ========================================
echo   VS Code Issues Fix
echo ========================================
echo.

echo [1/2] Fixing Maven Configuration...
cd backend
mvn clean compile -q
echo Maven configuration updated!

echo.
echo [2/2] VS Code SQL Server Extension Issue Detected
echo.
echo The SQL Server extension is causing crashes.
echo.
echo SOLUTIONS:
echo.
echo Option 1 - Disable SQL Server Extension:
echo   1. Open VS Code
echo   2. Go to Extensions (Ctrl+Shift+X)
echo   3. Search for "SQL Server (mssql)"
echo   4. Click "Disable"
echo.
echo Option 2 - Reinstall Extension:
echo   1. Uninstall SQL Server extension
echo   2. Restart VS Code
echo   3. Reinstall from Extensions marketplace
echo.
echo Option 3 - Update .NET Runtime:
echo   Download and install latest .NET Runtime from Microsoft
echo.
echo ========================================
echo   Next Steps for Maven Projects:
echo ========================================
echo.
echo 1. Open VS Code: code .
echo 2. Press Ctrl+Shift+P
echo 3. Run: "Java: Reload Projects"
echo 4. Run: "Maven: Update Project Configuration"
echo.
echo Your TECNO DRIVE project is ready!
echo.
pause
